# API Usage Examples (cURL)

Replace `{{base_url}}` with your server's base URL (e.g., http://localhost:8080).

---

## Fetch documents
```
curl -X GET "{{base_url}}/firestore/<collection>?dbId=<dbId>&id=<id>&timestamp=<timestamp>&intervalMinutes=<minutes>"
```

---

## Create or update document
```
curl -X POST "{{base_url}}/firestore/<collection>?dbId=<dbId>&id=<id>" \
  -H "Content-Type: application/json" \
  -d '{"field":"value"}'
```

---

## Delete documents by fields
```
curl -X DELETE "{{base_url}}/api/data/<collection>?field1=value1&field2=value2"
```

---

## Fetch data with filters
```
curl -X GET "{{base_url}}/api/data/<collection>?type=<type>&area=<area>&otherField=value"
```

---

## Mark an item as seen by a user
```
curl -X POST "{{base_url}}/api/seen" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user123","itemId":"item456","itemType":"event"}'
```

---

## Get all seen items for a user
```
curl -X GET "{{base_url}}/api/seen/user123"
```

---

For more details, see the main README or SEEN_FEATURE_README.md.


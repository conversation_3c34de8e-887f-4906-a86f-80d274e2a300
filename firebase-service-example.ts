import { FirestoreService, sanitizeId, normalizeQueryFilters } from './firebase';

// Example usage of the new FirestoreService class
async function exampleUsage() {
  const firestoreService = new FirestoreService();
  const dbId = 'pulse';
  const collection = 'events';

  try {
    // Example 1: Create or update a document
    console.log('Creating/updating a document...');
    const createResult = await firestoreService.createOrUpdateDocument(
      collection,
      'https://example.com/event/123',
      {
        title: 'Sample Event',
        category: 'conference',
        region: 'north-america',
        date: '2024-01-15',
        description: 'A sample event for demonstration'
      },
      dbId
    );
    console.log('Create result:', createResult);

    // Example 2: Get documents with timestamp filtering
    console.log('\nGetting documents with timestamp filtering...');
    const timestamp = new Date().toISOString();
    const intervalMinutes = 60; // Last hour
    const documents = await firestoreService.getDocuments(
      collection,
      null, // Get all documents
      dbId,
      timestamp,
      intervalMinutes
    );
    console.log('Documents from last hour:', documents);

    // Example 3: Get documents by fields with defaults
    console.log('\nGetting documents by fields with defaults...');
    const filteredDocs = await firestoreService.getDocumentsByFields(
      collection,
      dbId,
      { type: 'conference' }, // Will be normalized to category: 'conference'
      { region: 'north-america' } // Default region
    );
    console.log('Filtered documents:', filteredDocs);

    // Example 4: Batch upsert documents
    console.log('\nBatch upserting documents...');
    const batchDocs = [
      {
        id: 'event-1',
        title: 'Event 1',
        category: 'workshop',
        region: 'europe'
      },
      {
        id: 'event-2',
        title: 'Event 2',
        category: 'seminar',
        region: 'asia'
      }
    ];
    const batchResult = await firestoreService.batchUpsertDocuments(
      collection,
      batchDocs,
      dbId
    );
    console.log('Batch upsert result:', batchResult);

    // Example 5: Update a specific document
    console.log('\nUpdating a specific document...');
    const updateResult = await firestoreService.updateDocument(
      collection,
      'event-1',
      {
        title: 'Updated Event 1',
        status: 'active'
      },
      dbId
    );
    console.log('Update result:', updateResult);

    // Example 6: Delete documents by fields
    console.log('\nDeleting documents by fields...');
    const deletedCount = await firestoreService.deleteDocumentsByFields(
      dbId,
      collection,
      { category: 'workshop' }
    );
    console.log('Deleted documents count:', deletedCount);

    // Example 7: Delete contents by page
    console.log('\nDeleting contents by page...');
    const deletedByPage = await firestoreService.deleteContents(
      dbId,
      collection,
      'https://example.com/page/to/delete'
    );
    console.log('Deleted by page count:', deletedByPage);

  } catch (error) {
    console.error('Error in example usage:', error);
  }
}

// Example of utility functions
function utilityExamples() {
  console.log('\n=== Utility Function Examples ===');
  
  // sanitizeId examples
  console.log('sanitizeId examples:');
  console.log(sanitizeId('https://example.com/path/to/resource'));
  console.log(sanitizeId('http://test.com/some path/'));
  console.log(sanitizeId('simple-id'));

  // normalizeQueryFilters examples
  console.log('\nnormalizeQueryFilters examples:');
  const filters1 = { type: 'event', area: 'usa' };
  const defaults1 = { region: 'north-america' };
  console.log('Original:', filters1);
  console.log('Normalized:', normalizeQueryFilters(filters1, defaults1));

  const filters2 = { category: 'workshop' };
  const defaults2 = { type: 'event', region: 'europe' };
  console.log('Original:', filters2);
  console.log('Normalized:', normalizeQueryFilters(filters2, defaults2));
}

// Run examples if this file is executed directly
if (require.main === module) {
  console.log('=== FirestoreService Usage Examples ===');
  exampleUsage().then(() => {
    utilityExamples();
    console.log('\n=== Examples completed ===');
  });
}

export { exampleUsage, utilityExamples };

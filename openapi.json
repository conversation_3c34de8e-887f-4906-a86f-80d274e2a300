{"openapi": "3.0.0", "info": {"title": "Pulse API", "version": "1.0.0"}, "paths": {"/api/data/{collection}": {"get": {"summary": "Get documents from a collection with optional filters (sorted by rank, default 6 if absent)", "parameters": [{"name": "collection", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "area", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "List of documents"}}}, "delete": {"summary": "Delete documents by filters", "parameters": [{"name": "collection", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "area", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Delete count"}, "400": {"description": "Bad request"}}}}, "/openapi.json": {"get": {"summary": "Get OpenAPI specification", "responses": {"200": {"description": "OpenAPI document"}}}}}}
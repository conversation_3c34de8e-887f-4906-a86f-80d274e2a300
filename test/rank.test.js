/* eslint-env mocha */
const assert = require('assert');
const path = require('path');

const firebasePath = path.join(__dirname, '..', 'firebase.js');
const firebaseModule = require(firebasePath);
const originalGet = firebaseModule.getDocumentsByFields;

describe('rank sorting', () => {
  let server;
  let address;
  let request;

  before(done => {
    process.env.HTTP_PROXY = '';
    process.env.http_proxy = '';
    process.env.HTTPS_PROXY = '';
    process.env.https_proxy = '';
    // eslint-disable-next-line node/no-unpublished-require
    request = require('gaxios').request;
    firebaseModule.getDocumentsByFields = async () => [
      { id: 'a', rank: 2 },
      { id: 'b', rank: 1 },
      { id: 'c', rank: 3 }
    ];
    delete require.cache[require.resolve('../index')];
    const app = require('../index');
    server = app.listen(0, () => {
      address = `http://localhost:${server.address().port}`;
      done();
    });
  });

  after(done => {
    server.close(() => {
      firebaseModule.getDocumentsByFields = originalGet;
      done();
    });
  });

  it('sorts results by rank', async () => {
    const res = await request({ url: `${address}/api/data/test`, timeout: 5000 });
    assert.equal(res.status, 200);
    const ranks = res.data.map(r => r.rank);
    assert.deepStrictEqual(ranks, [1, 2, 3]);
  });

  it('treats missing rank as 6 when sorting', async () => {
    firebaseModule.getDocumentsByFields = async () => [
      { id: 'a', rank: 2 },
      { id: 'b' },
      { id: 'c', rank: 8 }
    ];
    const res = await request({ url: `${address}/api/data/test`, timeout: 5000 });
    assert.equal(res.status, 200);
    const ids = res.data.map(r => r.id);
    assert.deepStrictEqual(ids, ['a', 'b', 'c']);
  });
});

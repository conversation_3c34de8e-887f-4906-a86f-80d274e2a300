import admin from 'firebase-admin';
import path from 'path';

let serviceAccount: any;
if (!admin.apps.length) {
  serviceAccount = require(path.join(__dirname, './configs/firebase-service-account.json'));
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id,
  });
}

export function getFirestore(dbId: string) {
  return new admin.firestore.Firestore({
    projectId: admin.app().options.projectId,
    databaseId: dbId,
    credentials: serviceAccount,
  });
}

// Utility functions
export function normalizeQueryFilters(
  filters: Record<string, any>,
  defaults: Partial<Record<'type' | 'category' | 'region' | 'area', string>> = {}
): Record<string, any> {
  const mergedFilters = {...filters};
  for (const [key, value] of Object.entries(defaults)) {
    if (mergedFilters[key] === undefined) {
      mergedFilters[key] = value;
    }
  }

  if (mergedFilters.type !== undefined) {
    if (mergedFilters.category === undefined) {
      mergedFilters.category = mergedFilters.type;
    }
    delete mergedFilters.type;
  }

  if (mergedFilters.area !== undefined) {
    if (mergedFilters.region === undefined) {
      mergedFilters.region = mergedFilters.area;
    }
    delete mergedFilters.area;
  }

  return mergedFilters;
}

export function sanitizeId(id: string): string {
  return id
    .replace(/^https?:\/\//i, '') // Remove protocol (case-insensitive)
    .replace(/\/+$/, '') // Remove trailing slashes
    .replace(/[\/\s]+/g, '-'); // Replace slashes and spaces with dashes
}

// FirestoreService class with comprehensive utility methods
export class FirestoreService {
  async getDocuments(
    collection: string,
    id: string | null = null,
    dbId: string,
    timestamp: string | null = null,
    intervalMinutes: number | null = null
  ): Promise<any> {
    const db = getFirestore(dbId);

    // If intervalMinutes is provided, calculate the upper bound
    let lowerBound = null;
    if (timestamp && intervalMinutes) {
      const baseTime = new Date(timestamp);
      lowerBound = new Date(
        baseTime.getTime() - intervalMinutes * 60 * 1000
      ).toISOString();
    }

    if (id) {
      id = sanitizeId(id);
      const doc = await db.collection(collection).doc(id).get();
      if (!doc.exists) throw new Error('Document not found');
      const data = doc.data();
      if (timestamp && intervalMinutes && data && lowerBound) {
        const docTimestamp = data.timestamp;
        if (!docTimestamp || docTimestamp < lowerBound) {
          if (!docTimestamp) {
            //log docTimeStamp, lowerBound, basetime, id
            console.log(`Document ${id}`);
            console.log(`Lower bound: ${lowerBound}`);
            console.log(`Timestamp: ${timestamp}`);
          }

          return null;
        }
      }
      return data;
    } else {
      let query: FirebaseFirestore.Query = db.collection(collection);
      if (timestamp && intervalMinutes) {
        query = query.where('timestamp', '>=', lowerBound);
      } else if (timestamp) {
        query = query.where('timestamp', '>', timestamp);
      }
      const snapshot = await query.get();
      return snapshot.docs.map((doc) => ({id: doc.id, ...doc.data()}));
    }
  }

  async createOrUpdateDocument(
    collection: string,
    id: string,
    data: Record<string, any>,
    dbId: string
  ): Promise<{message: string}> {
    const db = getFirestore(dbId);
    id = sanitizeId(id);
    // Add or update the timestamp field
    data.id = id;
    data.timestamp = new Date().toISOString();
    await db.collection(collection).doc(id).set(data, {merge: true});
    return {message: 'Document created/updated successfully'};
  }

  async updateDocument(
    collection: string,
    id: string,
    data: Record<string, any>,
    dbId: string
  ): Promise<{message: string}> {
    if (!collection) {
      throw new Error('Collection is required');
    }
    if (!id) {
      throw new Error('id is required');
    }
    const db = getFirestore(dbId);
    id = sanitizeId(id);
    data.timestamp = data.timestamp || new Date().toISOString();
    await db.collection(collection).doc(id).set(data, {merge: true});
    return {message: 'Document updated successfully'};
  }

  async batchUpsertDocuments(
    collection: string,
    docs: Array<Record<string, any>>,
    dbId: string
  ): Promise<{message: string}> {
    // docs should be an array of objects, each with an `id` and `data` property
    const upserts = docs.map((doc) =>
      this.createOrUpdateDocument(collection, doc.id, doc, dbId)
    );
    await Promise.all(upserts);
    return {message: 'Batch upsert completed successfully'};
  }

  async getDocumentsByFields(
    collection: string,
    dbId: string,
    filters: Record<string, any> = {},
    defaults: Partial<Record<'type' | 'category' | 'region' | 'area', string>> = {}
  ): Promise<any[]> {
    if (!collection) {
      throw new Error('Collection is required');
    }
    const db = getFirestore(dbId);
    const mergedFilters = normalizeQueryFilters(filters, defaults);

    let query: FirebaseFirestore.Query = db.collection(collection);
    for (const [field, value] of Object.entries(mergedFilters)) {
      query = query.where(field, '==', value);
    }
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({id: doc.id, ...doc.data()}));
  }

  /**
   * Delete documents from a collection using arbitrary field filters.
   * A collection and at least one filter are required to avoid mass deletions.
   * @param {string} dbId - Firestore database ID
   * @param {string} collection - Firestore collection name
   * @param {object} filters - key/value pairs to filter on
   * @returns {Promise<number>} - Number of deleted documents
   */
  async deleteDocumentsByFields(
    dbId: string,
    collection: string,
    filters: Record<string, any> = {}
  ): Promise<number> {
    if (!collection) {
      throw new Error('Collection is required');
    }
    if (!filters || Object.keys(filters).length === 0) {
      throw new Error('At least one filter must be provided');
    }
    const db = getFirestore(dbId);
    let query: FirebaseFirestore.Query = db.collection(collection);
    for (const [field, value] of Object.entries(filters)) {
      query = query.where(field, '==', value);
    }
    const snapshot = await query.get();
    const batch = db.batch();
    snapshot.forEach((doc) => batch.delete(doc.ref));
    await batch.commit();
    return snapshot.size;
  }

  async deleteContents(
      dbId: string,
      collection: string,
      page: string
  ): Promise<number> {
    if (!collection) {
      throw new Error('Collection is required');
    }
    if (!page) {
      throw new Error('page is required');
    }

    const db = getFirestore(dbId);
    const snapshot = await db.collection(collection).get();
    const batch = db.batch();

    const encodedTarget = encodeURIComponent(page);
    let deleted = 0;

    snapshot.forEach(doc => {
      const docPage = doc.get('page');
      if (docPage && encodeURIComponent(docPage) === encodedTarget) {
        batch.delete(doc.ref);
        deleted++;
      }
    });

    if (deleted > 0) {
      await batch.commit();
    }

    return deleted;
  }
}

// Legacy function exports for backward compatibility
export async function getDocumentsByFields(
  dbId: string,
  collection: string,
  filters: Record<string, unknown> = {}
): Promise<any[]> {
  const db = getFirestore(dbId);
  let query: FirebaseFirestore.Query = db.collection(collection);
  for (const [field, value] of Object.entries(filters)) {
    query = query.where(field, '==', value);
  }
  const snapshot = await query.get();
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
}

export async function deleteDocumentsByFields(
  dbId: string,
  collection: string,
  filters: Record<string, unknown> = {}
): Promise<number> {
  if (!collection) {
    throw new Error('Collection is required');
  }
  if (!filters || Object.keys(filters).length === 0) {
    throw new Error('At least one filter must be provided');
  }
  const db = getFirestore(dbId);
  let query: FirebaseFirestore.Query = db.collection(collection);
  for (const [field, value] of Object.entries(filters)) {
    query = query.where(field, '==', value);
  }
  const snapshot = await query.get();
  const batch = db.batch();
  snapshot.forEach(doc => batch.delete(doc.ref));
  await batch.commit();
  return snapshot.size;
}


import express from 'express';
import { readFileSync } from 'fs';
import handlebars from 'handlebars';
// eslint-disable-next-line node/no-unpublished-require
import openapiSpec from './openapi.json';
// eslint-disable-next-line node/no-unpublished-require
import { getDocumentsByFields, deleteDocumentsByFields, getFirestore } from './firebase';
import { filterPastEvents } from './eventsFilter';

const app = express();

app.use('/assets', express.static('assets'));

const data = {
  service: process.env.K_SERVICE || '???',
  revision: process.env.K_REVISION || '???',
};
let template: handlebars.TemplateDelegate | undefined;

app.get('/', async (req, res) => {
  if (!template) {
    try {
      template = handlebars.compile(readFileSync('index.html.hbs', 'utf8'));
    } catch (e) {
      console.error(e);
      return res.status(500).send('Internal Server Error');
    }
  }

  try {
    const output = template(data);
    res.status(200).send(output);
  } catch (e) {
    console.error(e);
    res.status(500).send('Internal Server Error');
  }
});

app.get('/openapi.json', (_req, res) => {
  res.json(openapiSpec);
});

app.get('/api/data/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    const { type, area, ...rest } = req.query as Record<string, string>;
    const filters: Record<string, unknown> = { ...rest };
    if (type) filters.category = type;
    if (area) filters.region = area;
    let docs = await getDocumentsByFields('pulse', collection, filters);
    if (filters.category === 'events') {
      docs = filterPastEvents(docs);
    }
    if (docs.some(doc => doc.rank !== undefined)) {
      const defaultRank = 6;
      docs.sort((a, b) => {
        const aRank = a.rank ?? defaultRank;
        const bRank = b.rank ?? defaultRank;
        return aRank - bRank;
      });
    }
    res.json(docs);
  } catch (err) {
    console.error(err);
    res.status(500).send('Could not retrieve file');
  }
});

app.delete('/api/data/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    const { type, area, ...rest } = req.query as Record<string, string>;
    const filters: Record<string, unknown> = { ...rest };
    if (type) filters.category = type;
    if (area) filters.region = area;
    if (Object.keys(filters).length === 0) {
      return res.status(400).send('At least one filter is required');
    }
    const deletedCount = await deleteDocumentsByFields('pulse', collection, filters);
    res.json({ deleted: deletedCount });
  } catch (err) {
    console.error(err);
    res.status(500).send('Could not delete files');
  }
});

// Mark an item as seen by a user (enhanced with time tracking)
app.post('/api/seen', express.json(), async (req, res) => {
  const { userId, itemId, itemType, timeSpent, sessionId } = req.body;
  if (!userId || !itemId) {
    return res.status(400).json({ error: 'userId and itemId are required' });
  }
  try {
    const db = getFirestore('pulse');
    const docRef = db.collection('seen').doc(userId).collection('items').doc(itemId);

    // Get existing document to preserve time tracking data
    const existingDoc = await docRef.get();
    const existingData = existingDoc.exists ? existingDoc.data() : {};

    const updateData: any = {
      itemId,
      itemType: itemType || null,
      seenAt: new Date().toISOString(),
      lastSeenAt: new Date().toISOString(),
    };

    // Handle time tracking
    if (timeSpent !== undefined) {
      const currentTotalTime = existingData.totalTimeSpent || 0;
      updateData.totalTimeSpent = currentTotalTime + (timeSpent || 0);
      updateData.lastSessionTimeSpent = timeSpent;
      updateData.lastSessionId = sessionId || null;
    }

    // Track view count
    updateData.viewCount = (existingData.viewCount || 0) + 1;

    await docRef.set(updateData, { merge: true });
    res.json({
      success: true,
      totalTimeSpent: updateData.totalTimeSpent,
      viewCount: updateData.viewCount
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to mark as seen' });
  }
});

// Get all seen items for a user
app.get('/api/seen/:userId', async (req, res) => {
  const { userId } = req.params;
  try {
    const db = getFirestore('pulse');
    const snapshot = await db.collection('seen').doc(userId).collection('items').get();
    const items = snapshot.docs.map(doc => doc.data());
    res.json({ items });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to retrieve seen items' });
  }
});

const PORT = process.env.PORT || 8080;

if (require.main === module) {
  const server = app.listen(PORT, () => {
    console.log(
      `Hello from Cloud Run! The container started successfully and is listening for HTTP requests on ${PORT}`
    );
    if (process.env.PRECHECK) {
      setTimeout(() => {
        server.close(() => {
          console.log('Precheck server closed');
        });
      }, 1000);
    }
  });
}

export = app;
